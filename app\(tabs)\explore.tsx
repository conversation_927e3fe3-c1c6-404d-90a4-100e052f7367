import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Ionicons } from '@expo/vector-icons';
import Constants from 'expo-constants';
import * as Device from 'expo-device';
import { Accelerometer, Gyroscope, Magnetometer } from 'expo-sensors';
import React, { useEffect, useState } from 'react';
import { Dimensions, Platform, ScrollView, StyleSheet } from 'react-native';

const { width, height } = Dimensions.get('window');

export default function DeviceInfoScreen() {
  const [sensorAvailability, setSensorAvailability] = useState({
    accelerometer: false,
    gyroscope: false,
    magnetometer: false,
  });

  useEffect(() => {
    checkSensorAvailability();
  }, []);

  const checkSensorAvailability = async () => {
    const accelerometerAvailable = await Accelerometer.isAvailableAsync();
    const gyroscopeAvailable = await Gyroscope.isAvailableAsync();
    const magnetometerAvailable = await Magnetometer.isAvailableAsync();

    setSensorAvailability({
      accelerometer: accelerometerAvailable,
      gyroscope: gyroscopeAvailable,
      magnetometer: magnetometerAvailable,
    });
  };

  const InfoCard = ({ title, icon, iconColor, children, backgroundColor }: any) => (
    <ThemedView style={[styles.infoCard, { backgroundColor }]}>
      <ThemedView style={styles.cardHeader}>
        <Ionicons name={icon} size={28} color={iconColor} />
        <ThemedText type="subtitle" style={styles.cardTitle}>{title}</ThemedText>
      </ThemedView>
      {children}
    </ThemedView>
  );

  const InfoRow = ({ label, value, valueColor = '#FFFFFF' }: any) => (
    <ThemedView style={styles.infoRow}>
      <ThemedText style={styles.infoLabel}>{label}:</ThemedText>
      <ThemedText style={[styles.infoValue, { color: valueColor }]}>{value}</ThemedText>
    </ThemedView>
  );

  const SensorStatus = ({ name, available }: any) => (
    <ThemedView style={styles.sensorRow}>
      <ThemedText style={styles.sensorName}>{name}</ThemedText>
      <ThemedView style={styles.sensorStatusContainer}>
        <ThemedView style={[
          styles.sensorIndicator,
          { backgroundColor: available ? '#4ECDC4' : '#E74C3C' }
        ]} />
        <ThemedText style={styles.sensorStatus}>
          {available ? 'Available' : 'Not Available'}
        </ThemedText>
      </ThemedView>
    </ThemedView>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <ThemedView style={styles.header}>
        <ThemedText type="title" style={styles.headerTitle}>📱 Device Info</ThemedText>
        <ThemedText style={styles.headerSubtitle}>Explore your device capabilities</ThemedText>
      </ThemedView>

      {/* Device Information Card */}
      <InfoCard
        title="Device Details"
        icon="phone-portrait-outline"
        iconColor="#FF6B6B"
        backgroundColor="#FF6B6B"
      >
        <InfoRow label="Device Name" value={Device.deviceName || 'Unknown'} />
        <InfoRow label="Brand" value={Device.brand || 'Unknown'} />
        <InfoRow label="Model" value={Device.modelName || 'Unknown'} />
        <InfoRow label="OS" value={Device.osName || 'Unknown'} />
        <InfoRow label="OS Version" value={Device.osVersion || 'Unknown'} />
        <InfoRow label="Platform" value={Platform.OS} />
        <InfoRow label="Device Type" value={Device.deviceType?.toString() || 'Unknown'} />
      </InfoCard>

      {/* Screen Information Card */}
      <InfoCard
        title="Screen Specs"
        icon="tablet-landscape-outline"
        iconColor="#4ECDC4"
        backgroundColor="#4ECDC4"
      >
        <InfoRow label="Width" value={`${width.toFixed(0)} px`} />
        <InfoRow label="Height" value={`${height.toFixed(0)} px`} />
        <InfoRow label="Pixel Ratio" value={`${Dimensions.get('window').scale}x`} />
        <InfoRow label="Font Scale" value={`${Dimensions.get('window').fontScale}x`} />
      </InfoCard>

      {/* Sensor Availability Card */}
      <InfoCard
        title="Sensor Status"
        icon="hardware-chip-outline"
        iconColor="#45B7D1"
        backgroundColor="#45B7D1"
      >
        <SensorStatus name="Accelerometer" available={sensorAvailability.accelerometer} />
        <SensorStatus name="Gyroscope" available={sensorAvailability.gyroscope} />
        <SensorStatus name="Magnetometer" available={sensorAvailability.magnetometer} />
      </InfoCard>

      {/* App Information Card */}
      <InfoCard
        title="App Details"
        icon="information-circle-outline"
        iconColor="#F39C12"
        backgroundColor="#F39C12"
      >
        <InfoRow label="App Name" value={Constants.expoConfig?.name || 'Sensor Test Lab'} />
        <InfoRow label="Version" value={Constants.expoConfig?.version || '1.0.0'} />
        <InfoRow label="Expo SDK" value={Constants.expoConfig?.sdkVersion || 'Unknown'} />
        <InfoRow label="Platform" value={Constants.platform?.ios ? 'iOS' : 'Android'} />
      </InfoCard>

      {/* Performance Tips Card */}
      <InfoCard
        title="Performance Tips"
        icon="flash-outline"
        iconColor="#9B59B6"
        backgroundColor="#9B59B6"
      >
        <ThemedView style={styles.tipContainer}>
          <ThemedText style={styles.tipText}>💡 Sensor data updates at 100ms intervals</ThemedText>
          <ThemedText style={styles.tipText}>🔋 Stop sensors when not needed to save battery</ThemedText>
          <ThemedText style={styles.tipText}>📱 Haptic feedback works best on physical devices</ThemedText>
          <ThemedText style={styles.tipText}>🎯 Move your device to see sensor values change</ThemedText>
        </ThemedView>
      </InfoCard>

      <ThemedView style={styles.footer}>
        <ThemedText style={styles.footerText}>
          🚀 Built with Expo • React Native
        </ThemedText>
        <ThemedText style={styles.footerSubtext}>
          Sensor Test Lab v1.0.0
        </ThemedText>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1A1A2E',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 30,
    backgroundColor: '#667eea',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    // Cross-platform shadow
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
      web: {
        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
      },
    }),
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#F0F0F0',
    textAlign: 'center',
    opacity: 1,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  infoCard: {
    marginHorizontal: 20,
    marginVertical: 12,
    padding: 24,
    borderRadius: 20,
    // Cross-platform shadow
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
      },
      android: {
        elevation: 8,
      },
      web: {
        boxShadow: '0 6px 12px rgba(0, 0, 0, 0.15)',
      },
    }),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginLeft: 12,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    paddingHorizontal: 8,
    borderRadius: 6,
    marginBottom: 4,
  },
  infoLabel: {
    fontSize: 16,
    color: '#F5F5F5',
    flex: 1,
    fontWeight: '500',
    textShadowColor: 'rgba(0, 0, 0, 0.4)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
    textAlign: 'right',
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    textShadowColor: 'rgba(0, 0, 0, 0.6)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  sensorRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 6,
    marginBottom: 4,
  },
  sensorName: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  sensorStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sensorIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 8,
  },
  sensorStatus: {
    fontSize: 14,
    color: '#F0F0F0',
    fontWeight: '600',
    textShadowColor: 'rgba(0, 0, 0, 0.4)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  tipContainer: {
    paddingVertical: 8,
  },
  tipText: {
    fontSize: 14,
    color: '#F5F5F5',
    marginBottom: 12,
    lineHeight: 22,
    textShadowColor: 'rgba(0, 0, 0, 0.4)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
    paddingHorizontal: 4,
  },
  footer: {
    paddingVertical: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#BDC3C7',
    textAlign: 'center',
    marginBottom: 4,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  footerSubtext: {
    fontSize: 12,
    color: '#95A5A6',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
});
