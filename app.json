{"expo": {"name": "santri-mental", "slug": "santri-mental", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "santrimental", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "69f4110c-8221-4436-8e9b-0751b3bbf05b"}}, "owner": "roniwahyu1"}}