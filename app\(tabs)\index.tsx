import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { Accelerometer, Gyroscope, Magnetometer } from 'expo-sensors';
import React, { useEffect, useState } from 'react';
import { Alert, Dimensions, ScrollView, TouchableOpacity } from 'react-native';

const { width } = Dimensions.get('window');

interface SensorData {
  x: number;
  y: number;
  z: number;
}

export default function SensorTestScreen() {
  const [accelerometerData, setAccelerometerData] = useState<SensorData>({ x: 0, y: 0, z: 0 });
  const [gyroscopeData, setGyroscopeData] = useState<SensorData>({ x: 0, y: 0, z: 0 });
  const [magnetometerData, setMagnetometerData] = useState<SensorData>({ x: 0, y: 0, z: 0 });
  const [isAccelerometerActive, setIsAccelerometerActive] = useState(false);
  const [isGyroscopeActive, setIsGyroscopeActive] = useState(false);
  const [isMagnetometerActive, setIsMagnetometerActive] = useState(false);

  useEffect(() => {
    return () => {
      Accelerometer.removeAllListeners();
      Gyroscope.removeAllListeners();
      Magnetometer.removeAllListeners();
    };
  }, []);

  const toggleAccelerometer = async () => {
    if (isAccelerometerActive) {
      Accelerometer.removeAllListeners();
      setIsAccelerometerActive(false);
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } else {
      const isAvailable = await Accelerometer.isAvailableAsync();
      if (isAvailable) {
        Accelerometer.setUpdateInterval(100);
        Accelerometer.addListener(setAccelerometerData);
        setIsAccelerometerActive(true);
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      } else {
        Alert.alert('Accelerometer not available', 'This device does not support accelerometer.');
      }
    }
  };

  const toggleGyroscope = async () => {
    if (isGyroscopeActive) {
      Gyroscope.removeAllListeners();
      setIsGyroscopeActive(false);
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } else {
      const isAvailable = await Gyroscope.isAvailableAsync();
      if (isAvailable) {
        Gyroscope.setUpdateInterval(100);
        Gyroscope.addListener(setGyroscopeData);
        setIsGyroscopeActive(true);
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      } else {
        Alert.alert('Gyroscope not available', 'This device does not support gyroscope.');
      }
    }
  };

  const toggleMagnetometer = async () => {
    if (isMagnetometerActive) {
      Magnetometer.removeAllListeners();
      setIsMagnetometerActive(false);
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } else {
      const isAvailable = await Magnetometer.isAvailableAsync();
      if (isAvailable) {
        Magnetometer.setUpdateInterval(100);
        Magnetometer.addListener(setMagnetometerData);
        setIsMagnetometerActive(true);
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      } else {
        Alert.alert('Magnetometer not available', 'This device does not support magnetometer.');
      }
    }
  };

  const testHaptics = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    setTimeout(async () => {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }, 200);
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <ThemedView style={styles.header}>
        <ThemedText type="title" style={styles.headerTitle}>📱 Sensor Test Lab</ThemedText>
        <ThemedText style={styles.headerSubtitle}>Test your device's native sensors</ThemedText>
      </ThemedView>

      {/* Accelerometer Card */}
      <TouchableOpacity style={[styles.card, styles.accelerometerCard]} onPress={toggleAccelerometer}>
        <ThemedView style={styles.cardHeader}>
          <Ionicons name="speedometer-outline" size={32} color="#FF6B6B" />
          <ThemedText type="subtitle" style={styles.cardTitle}>Accelerometer</ThemedText>
          <ThemedView style={[styles.statusIndicator, { backgroundColor: isAccelerometerActive ? '#4ECDC4' : '#95A5A6' }]} />
        </ThemedView>
        <ThemedText style={styles.cardDescription}>Measures device acceleration</ThemedText>
        <ThemedView style={styles.dataContainer}>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>X:</ThemedText>
            <ThemedText style={styles.dataValue}>{accelerometerData.x.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Y:</ThemedText>
            <ThemedText style={styles.dataValue}>{accelerometerData.y.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Z:</ThemedText>
            <ThemedText style={styles.dataValue}>{accelerometerData.z.toFixed(3)}</ThemedText>
          </ThemedView>
        </ThemedView>
        <ThemedText style={styles.tapHint}>
          {isAccelerometerActive ? 'Tap to stop' : 'Tap to start'}
        </ThemedText>
      </TouchableOpacity>

      {/* Gyroscope Card */}
      <TouchableOpacity style={[styles.card, styles.gyroscopeCard]} onPress={toggleGyroscope}>
        <ThemedView style={styles.cardHeader}>
          <Ionicons name="refresh-circle-outline" size={32} color="#4ECDC4" />
          <ThemedText type="subtitle" style={styles.cardTitle}>Gyroscope</ThemedText>
          <ThemedView style={[styles.statusIndicator, { backgroundColor: isGyroscopeActive ? '#4ECDC4' : '#95A5A6' }]} />
        </ThemedView>
        <ThemedText style={styles.cardDescription}>Measures device rotation</ThemedText>
        <ThemedView style={styles.dataContainer}>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>X:</ThemedText>
            <ThemedText style={styles.dataValue}>{gyroscopeData.x.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Y:</ThemedText>
            <ThemedText style={styles.dataValue}>{gyroscopeData.y.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Z:</ThemedText>
            <ThemedText style={styles.dataValue}>{gyroscopeData.z.toFixed(3)}</ThemedText>
          </ThemedView>
        </ThemedView>
        <ThemedText style={styles.tapHint}>
          {isGyroscopeActive ? 'Tap to stop' : 'Tap to start'}
        </ThemedText>
      </TouchableOpacity>

      {/* Magnetometer Card */}
      <TouchableOpacity style={[styles.card, styles.magnetometerCard]} onPress={toggleMagnetometer}>
        <ThemedView style={styles.cardHeader}>
          <Ionicons name="compass-outline" size={32} color="#45B7D1" />
          <ThemedText type="subtitle" style={styles.cardTitle}>Magnetometer</ThemedText>
          <ThemedView style={[styles.statusIndicator, { backgroundColor: isMagnetometerActive ? '#4ECDC4' : '#95A5A6' }]} />
        </ThemedView>
        <ThemedText style={styles.cardDescription}>Measures magnetic field</ThemedText>
        <ThemedView style={styles.dataContainer}>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>X:</ThemedText>
            <ThemedText style={styles.dataValue}>{magnetometerData.x.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Y:</ThemedText>
            <ThemedText style={styles.dataValue}>{magnetometerData.y.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Z:</ThemedText>
            <ThemedText style={styles.dataValue}>{magnetometerData.z.toFixed(3)}</ThemedText>
          </ThemedView>
        </ThemedView>
        <ThemedText style={styles.tapHint}>
          {isMagnetometerActive ? 'Tap to stop' : 'Tap to start'}
        </ThemedText>
      </TouchableOpacity>

      {/* Haptics Test Card */}
      <TouchableOpacity style={[styles.card, styles.hapticsCard]} onPress={testHaptics}>
        <ThemedView style={styles.cardHeader}>
          <Ionicons name="phone-portrait-outline" size={32} color="#F39C12" />
          <ThemedText type="subtitle" style={styles.cardTitle}>Haptic Feedback</ThemedText>
          <Ionicons name="hand-left-outline" size={24} color="#F39C12" />
        </ThemedView>
        <ThemedText style={styles.cardDescription}>Test device vibration patterns</ThemedText>
        <ThemedView style={styles.hapticsInfo}>
          <ThemedText style={styles.hapticsText}>Tap to feel the vibration!</ThemedText>
          <ThemedText style={styles.hapticsSubtext}>Heavy impact + Success notification</ThemedText>
        </ThemedView>
      </TouchableOpacity>

      <ThemedView style={styles.footer}>
        <ThemedText style={styles.footerText}>
          🚀 Built with Expo • React Native
        </ThemedText>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 30,
    backgroundColor: '#667eea',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#E8E8E8',
    textAlign: 'center',
    opacity: 0.9,
  },
  card: {
    marginHorizontal: 20,
    marginVertical: 12,
    padding: 24,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  accelerometerCard: {
    backgroundColor: '#FF6B6B',
  },
  gyroscopeCard: {
    backgroundColor: '#4ECDC4',
  },
  magnetometerCard: {
    backgroundColor: '#45B7D1',
  },
  hapticsCard: {
    backgroundColor: '#F39C12',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
    marginLeft: 12,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  cardDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 20,
    lineHeight: 20,
  },
  dataContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  dataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  dataLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    minWidth: 20,
  },
  dataValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontFamily: 'monospace',
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 8,
    minWidth: 80,
    textAlign: 'center',
  },
  tapHint: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  hapticsInfo: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  hapticsText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
  },
  hapticsSubtext: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  footer: {
    paddingVertical: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#95A5A6',
    textAlign: 'center',
  },
});
