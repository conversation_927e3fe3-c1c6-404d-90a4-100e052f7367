import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { Accelerometer, Gyroscope, Magnetometer } from 'expo-sensors';
import React, { useEffect, useState } from 'react';
import { Alert, Dimensions, Platform, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';

const { width } = Dimensions.get('window');

interface SensorData {
  x: number;
  y: number;
  z: number;
}

export default function SensorTestScreen() {
  const [accelerometerData, setAccelerometerData] = useState<SensorData>({ x: 0, y: 0, z: 0 });
  const [gyroscopeData, setGyroscopeData] = useState<SensorData>({ x: 0, y: 0, z: 0 });
  const [magnetometerData, setMagnetometerData] = useState<SensorData>({ x: 0, y: 0, z: 0 });
  const [isAccelerometerActive, setIsAccelerometerActive] = useState(false);
  const [isGyroscopeActive, setIsGyroscopeActive] = useState(false);
  const [isMagnetometerActive, setIsMagnetometerActive] = useState(false);
  const [sensorAvailability, setSensorAvailability] = useState({
    accelerometer: false,
    gyroscope: false,
    magnetometer: false,
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkSensorAvailability();
    return () => {
      Accelerometer.removeAllListeners();
      Gyroscope.removeAllListeners();
      Magnetometer.removeAllListeners();
    };
  }, []);

  const checkSensorAvailability = async () => {
    try {
      console.log('Checking sensor availability...');
      const accelerometerAvailable = await Accelerometer.isAvailableAsync();
      const gyroscopeAvailable = await Gyroscope.isAvailableAsync();
      const magnetometerAvailable = await Magnetometer.isAvailableAsync();

      console.log('Sensor availability:', {
        accelerometer: accelerometerAvailable,
        gyroscope: gyroscopeAvailable,
        magnetometer: magnetometerAvailable,
      });

      setSensorAvailability({
        accelerometer: accelerometerAvailable,
        gyroscope: gyroscopeAvailable,
        magnetometer: magnetometerAvailable,
      });
    } catch (error) {
      console.error('Error checking sensor availability:', error);
      Alert.alert('Error', 'Failed to check sensor availability');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleAccelerometer = async () => {
    try {
      if (isAccelerometerActive) {
        Accelerometer.removeAllListeners();
        setIsAccelerometerActive(false);
        console.log('Accelerometer stopped');
        if (Platform.OS !== 'web') {
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
      } else {
        console.log('Starting accelerometer...');
        if (!sensorAvailability.accelerometer) {
          Alert.alert(
            'Sensor Not Available',
            Platform.OS === 'web'
              ? 'Accelerometer is not available in web browsers. Please test on a physical device.'
              : 'This device does not support accelerometer.'
          );
          return;
        }

        Accelerometer.setUpdateInterval(100);
        const subscription = Accelerometer.addListener((data) => {
          console.log('Accelerometer data:', data);
          setAccelerometerData(data);
        });
        setIsAccelerometerActive(true);
        console.log('Accelerometer started');

        if (Platform.OS !== 'web') {
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        }
      }
    } catch (error) {
      console.error('Accelerometer error:', error);
      Alert.alert('Error', 'Failed to toggle accelerometer: ' + error.message);
    }
  };

  const toggleGyroscope = async () => {
    try {
      if (isGyroscopeActive) {
        Gyroscope.removeAllListeners();
        setIsGyroscopeActive(false);
        console.log('Gyroscope stopped');
        if (Platform.OS !== 'web') {
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
      } else {
        console.log('Starting gyroscope...');
        if (!sensorAvailability.gyroscope) {
          Alert.alert(
            'Sensor Not Available',
            Platform.OS === 'web'
              ? 'Gyroscope is not available in web browsers. Please test on a physical device.'
              : 'This device does not support gyroscope.'
          );
          return;
        }

        Gyroscope.setUpdateInterval(100);
        const subscription = Gyroscope.addListener((data) => {
          console.log('Gyroscope data:', data);
          setGyroscopeData(data);
        });
        setIsGyroscopeActive(true);
        console.log('Gyroscope started');

        if (Platform.OS !== 'web') {
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        }
      }
    } catch (error) {
      console.error('Gyroscope error:', error);
      Alert.alert('Error', 'Failed to toggle gyroscope: ' + error.message);
    }
  };

  const toggleMagnetometer = async () => {
    try {
      if (isMagnetometerActive) {
        Magnetometer.removeAllListeners();
        setIsMagnetometerActive(false);
        console.log('Magnetometer stopped');
        if (Platform.OS !== 'web') {
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
      } else {
        console.log('Starting magnetometer...');
        if (!sensorAvailability.magnetometer) {
          Alert.alert(
            'Sensor Not Available',
            Platform.OS === 'web'
              ? 'Magnetometer is not available in web browsers. Please test on a physical device.'
              : 'This device does not support magnetometer.'
          );
          return;
        }

        Magnetometer.setUpdateInterval(100);
        const subscription = Magnetometer.addListener((data) => {
          console.log('Magnetometer data:', data);
          setMagnetometerData(data);
        });
        setIsMagnetometerActive(true);
        console.log('Magnetometer started');

        if (Platform.OS !== 'web') {
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        }
      }
    } catch (error) {
      console.error('Magnetometer error:', error);
      Alert.alert('Error', 'Failed to toggle magnetometer: ' + error.message);
    }
  };

  const testHaptics = async () => {
    try {
      if (Platform.OS === 'web') {
        Alert.alert('Haptic Feedback', 'Haptic feedback is not available in web browsers. Please test on a physical device.');
        return;
      }

      console.log('Testing haptic feedback...');
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
      setTimeout(async () => {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }, 200);
      console.log('Haptic feedback completed');
    } catch (error) {
      console.error('Haptic error:', error);
      Alert.alert('Error', 'Failed to test haptic feedback: ' + error.message);
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <ThemedView style={styles.header}>
        <ThemedText type="title" style={styles.headerTitle}>📱 Sensor Test Lab</ThemedText>
        <ThemedText style={styles.headerSubtitle}>Test your device's native sensors</ThemedText>
      </ThemedView>

      {/* Accelerometer Card */}
      <TouchableOpacity style={[styles.card, styles.accelerometerCard]} onPress={toggleAccelerometer}>
        <ThemedView style={styles.cardHeader}>
          <Ionicons name="speedometer-outline" size={32} color="#FF6B6B" />
          <ThemedText type="subtitle" style={styles.cardTitle}>Accelerometer</ThemedText>
          <ThemedView style={[styles.statusIndicator, { backgroundColor: isAccelerometerActive ? '#4ECDC4' : sensorAvailability.accelerometer ? '#95A5A6' : '#E74C3C' }]} />
        </ThemedView>
        <ThemedText style={styles.cardDescription}>
          {isLoading ? 'Checking availability...' :
           sensorAvailability.accelerometer ? 'Measures device acceleration' :
           Platform.OS === 'web' ? 'Not available in web browser' : 'Not supported on this device'}
        </ThemedText>
        <ThemedView style={styles.dataContainer}>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>X:</ThemedText>
            <ThemedText style={styles.dataValue}>{accelerometerData.x.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Y:</ThemedText>
            <ThemedText style={styles.dataValue}>{accelerometerData.y.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Z:</ThemedText>
            <ThemedText style={styles.dataValue}>{accelerometerData.z.toFixed(3)}</ThemedText>
          </ThemedView>
        </ThemedView>
        <ThemedText style={styles.tapHint}>
          {isLoading ? 'Loading...' :
           !sensorAvailability.accelerometer ? 'Not Available' :
           isAccelerometerActive ? 'Tap to stop' : 'Tap to start'}
        </ThemedText>
      </TouchableOpacity>

      {/* Gyroscope Card */}
      <TouchableOpacity style={[styles.card, styles.gyroscopeCard]} onPress={toggleGyroscope}>
        <ThemedView style={styles.cardHeader}>
          <Ionicons name="refresh-circle-outline" size={32} color="#4ECDC4" />
          <ThemedText type="subtitle" style={styles.cardTitle}>Gyroscope</ThemedText>
          <ThemedView style={[styles.statusIndicator, { backgroundColor: isGyroscopeActive ? '#4ECDC4' : '#95A5A6' }]} />
        </ThemedView>
        <ThemedText style={styles.cardDescription}>Measures device rotation</ThemedText>
        <ThemedView style={styles.dataContainer}>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>X:</ThemedText>
            <ThemedText style={styles.dataValue}>{gyroscopeData.x.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Y:</ThemedText>
            <ThemedText style={styles.dataValue}>{gyroscopeData.y.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Z:</ThemedText>
            <ThemedText style={styles.dataValue}>{gyroscopeData.z.toFixed(3)}</ThemedText>
          </ThemedView>
        </ThemedView>
        <ThemedText style={styles.tapHint}>
          {isGyroscopeActive ? 'Tap to stop' : 'Tap to start'}
        </ThemedText>
      </TouchableOpacity>

      {/* Magnetometer Card */}
      <TouchableOpacity style={[styles.card, styles.magnetometerCard]} onPress={toggleMagnetometer}>
        <ThemedView style={styles.cardHeader}>
          <Ionicons name="compass-outline" size={32} color="#45B7D1" />
          <ThemedText type="subtitle" style={styles.cardTitle}>Magnetometer</ThemedText>
          <ThemedView style={[styles.statusIndicator, { backgroundColor: isMagnetometerActive ? '#4ECDC4' : '#95A5A6' }]} />
        </ThemedView>
        <ThemedText style={styles.cardDescription}>Measures magnetic field</ThemedText>
        <ThemedView style={styles.dataContainer}>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>X:</ThemedText>
            <ThemedText style={styles.dataValue}>{magnetometerData.x.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Y:</ThemedText>
            <ThemedText style={styles.dataValue}>{magnetometerData.y.toFixed(3)}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.dataRow}>
            <ThemedText style={styles.dataLabel}>Z:</ThemedText>
            <ThemedText style={styles.dataValue}>{magnetometerData.z.toFixed(3)}</ThemedText>
          </ThemedView>
        </ThemedView>
        <ThemedText style={styles.tapHint}>
          {isMagnetometerActive ? 'Tap to stop' : 'Tap to start'}
        </ThemedText>
      </TouchableOpacity>

      {/* Haptics Test Card */}
      <TouchableOpacity style={[styles.card, styles.hapticsCard]} onPress={testHaptics}>
        <ThemedView style={styles.cardHeader}>
          <Ionicons name="phone-portrait-outline" size={32} color="#F39C12" />
          <ThemedText type="subtitle" style={styles.cardTitle}>Haptic Feedback</ThemedText>
          <Ionicons name="hand-left-outline" size={24} color="#F39C12" />
        </ThemedView>
        <ThemedText style={styles.cardDescription}>Test device vibration patterns</ThemedText>
        <ThemedView style={styles.hapticsInfo}>
          <ThemedText style={styles.hapticsText}>Tap to feel the vibration!</ThemedText>
          <ThemedText style={styles.hapticsSubtext}>Heavy impact + Success notification</ThemedText>
        </ThemedView>
      </TouchableOpacity>

      <ThemedView style={styles.footer}>
        <ThemedText style={styles.footerText}>
          🚀 Built with Expo • React Native
        </ThemedText>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1A1A2E',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 30,
    backgroundColor: '#667eea',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    // Cross-platform shadow
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
      web: {
        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
      },
    }),
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#F0F0F0',
    textAlign: 'center',
    opacity: 1,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  card: {
    marginHorizontal: 20,
    marginVertical: 12,
    padding: 24,
    borderRadius: 20,
    // Cross-platform shadow
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
      },
      android: {
        elevation: 8,
      },
      web: {
        boxShadow: '0 6px 12px rgba(0, 0, 0, 0.15)',
      },
    }),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  accelerometerCard: {
    backgroundColor: '#FF6B6B',
  },
  gyroscopeCard: {
    backgroundColor: '#4ECDC4',
  },
  magnetometerCard: {
    backgroundColor: '#45B7D1',
  },
  hapticsCard: {
    backgroundColor: '#F39C12',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
    marginLeft: 12,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  cardDescription: {
    fontSize: 14,
    color: '#F5F5F5',
    marginBottom: 20,
    lineHeight: 20,
    textShadowColor: 'rgba(0, 0, 0, 0.4)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  dataContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  dataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  dataLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    minWidth: 20,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  dataValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontFamily: 'monospace',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    minWidth: 80,
    textAlign: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    textShadowColor: 'rgba(0, 0, 0, 0.7)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  tapHint: {
    fontSize: 14,
    color: '#F0F0F0',
    textAlign: 'center',
    fontStyle: 'italic',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  hapticsInfo: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  hapticsText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  hapticsSubtext: {
    fontSize: 14,
    color: '#F5F5F5',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.4)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  footer: {
    paddingVertical: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#BDC3C7',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
});
